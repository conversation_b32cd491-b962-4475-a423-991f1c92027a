{"name": "飞书助手-飞书(lark)导出", "description": "强大的飞书（lark）文档导出工具，支持导出pdf、word、markdown等多种格式", "private": true, "version": "0.1.2", "type": "module", "scripts": {"dev": "wxt", "dev:firefox": "wxt -b firefox", "build": "wxt build", "build:firefox": "wxt build -b firefox", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "compile": "vue-tsc --noEmit", "postinstall": "wxt prepare"}, "dependencies": {"@webext-core/messaging": "^2.2.0", "@zip.js/zip.js": "^2.7.62", "@zumer/snapdom": "^1.2.2", "browser-fs-access": "^0.37.0", "filenamify": "^6.0.0", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "js-base64": "^3.7.7", "jspdf": "^3.0.1", "jszip": "^3.10.1", "lodash-es": "^4.17.21", "markdown-docx": "^1.2.0", "markdown-it": "^14.1.0", "mdast-util-gfm-strikethrough": "^2.0.0", "mdast-util-gfm-table": "^2.0.0", "mdast-util-gfm-task-list-item": "^2.0.0", "mdast-util-math": "^3.0.0", "mdast-util-to-markdown": "^2.1.2", "radash": "^12.1.0", "vue": "^3.5.13"}, "devDependencies": {"@tailwindcss/vite": "^4.0.6", "@types/lodash-es": "^4.17.12", "@types/markdown-it": "^14.1.2", "@types/mdast": "^4.0.4", "@wxt-dev/auto-icons": "^1.0.2", "@wxt-dev/module-vue": "^1.0.2", "tailwindcss": "^4.0.6", "typescript": "5.6.3", "vue-tsc": "^2.2.10", "wxt": "^0.20.6"}}