import { markdownToHtml } from "@/pkg/utils/markdown-to-html";
import { Docx, docx } from "@/pkg/lark/docx";
import { convertDocxToHtmlWithImages } from "@/pkg/lark/html-converter-fn";
import { Toast } from "@/pkg/lark/env";
import { downloadWithFileSave } from "../utils/file-download";

import { exportOriginalPDF } from "./origin-pdf-export";
import { prepareExportData } from "./prepare-util";

/**
 * 创建完整的HTML页面用于在新窗口中预览
 */
function createPreviewHTML(bodyContent: string, title: string): string {
  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${title}</title>
  <style>
    * {
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
      color: #333;
    }
    
    .preview-container {
      max-width: 840px;
      margin: 0 auto;
      background: white;
      padding: 40px;
      box-shadow: 0 0 20px rgba(0,0,0,0.1);
      border-radius: 8px;
    }
    
         .preview-header {
       position: relative;
       text-align: center;
       margin-bottom: 30px;
       padding-bottom: 20px;
       border-bottom: 2px solid #eee;
     }
     
     .preview-title {
       font-size: 24px;
       font-weight: bold;
       color: #2c3e50;
       margin: 0;
     }
     
     .preview-subtitle {
       font-size: 14px;
       color: #7f8c8d;
       margin: 10px 0 0 0;
     }
     
     .usage-tips {
       background: #e8f4fd;
       border: 1px solid #bee5eb;
       border-radius: 8px;
       padding: 15px;
       margin: 20px 0;
       font-size: 14px;
       color: #0c5460;
       line-height: 1.5;
       text-align: left;
     }
     
     .usage-tips h3 {
       margin: 0 0 10px 0;
       font-size: 16px;
       color: #0c5460;
       font-weight: 600;
     }
     
     .usage-tips ul {
       margin: 10px 0;
       padding-left: 20px;
     }
     
     .usage-tips li {
       margin: 5px 0;
     }
     
     .usage-tips .highlight {
       background: #fff3cd;
       padding: 2px 4px;
       border-radius: 3px;
       font-weight: 500;
     }
     
     .print-button {
       position: absolute;
       top: 0;
       right: 0;
       background: #28a745;
       color: white;
       border: none;
       padding: 12px 24px;
       border-radius: 8px;
       cursor: pointer;
       font-size: 14px;
       font-weight: 600;
       transition: all 0.3s ease;
       box-shadow: 0 3px 6px rgba(40, 167, 69, 0.3);
       display: flex;
       align-items: center;
       gap: 8px;
     }
     
     .print-button:hover {
       background: #218838;
       transform: translateY(-2px);
       box-shadow: 0 6px 12px rgba(40, 167, 69, 0.4);
     }
     
     .print-button:active {
       transform: translateY(0);
       box-shadow: 0 3px 6px rgba(40, 167, 69, 0.3);
     }
     
     .print-button-pulse {
       animation: buttonPulse 2s infinite;
     }
     
     @keyframes buttonPulse {
       0% {
         box-shadow: 0 3px 6px rgba(40, 167, 69, 0.3);
       }
       50% {
         box-shadow: 0 3px 6px rgba(40, 167, 69, 0.3), 0 0 0 10px rgba(40, 167, 69, 0.1);
       }
       100% {
         box-shadow: 0 3px 6px rgba(40, 167, 69, 0.3);
       }
     }
    
    .content-wrapper {
      width: 100%;
    }
    
  
    
    /* 连续图片容器样式 */
    .content-wrapper .consecutive-images {
      display: flex !important;
      flex-wrap: nowrap !important;
      align-items: flex-start !important;
      gap: 10px !important;
      margin: 20px 0 10px 0 !important;
      overflow: hidden !important;
    }
    
    .content-wrapper .consecutive-images img {
      flex: 1 1 auto !important;
      min-width: 0 !important;
      max-width: none !important;
      width: auto !important;
      height: auto !important;
      object-fit: contain !important;
      margin: 0 !important;
      display: block !important;
    }
    
    .content-wrapper h1, 
    .content-wrapper h2, 
    .content-wrapper h3, 
    .content-wrapper h4, 
    .content-wrapper h5, 
    .content-wrapper h6 {
      margin-top: 30px;
      margin-bottom: 15px;
      line-height: 1.4;
    }
    
    .content-wrapper p {
      margin: 15px 0;
    }
    
    .content-wrapper hr {
      margin: 15px 0;
      border: none;
      border-top: 1px solid #ddd;
      height: 1px;
    }
    
    .content-wrapper table {
      margin: 10px 0;
      border-collapse: collapse;
      width: 100%;
    }
    
    .content-wrapper table th,
    .content-wrapper table td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    
    .content-wrapper table th {
      background-color: #f2f2f2;
      font-weight: bold;
    }
    
    .content-wrapper blockquote {
      margin: 10px 0;
      padding: 10px 15px;
      background-color: #f9f9f9;
      border-left: 4px solid #ddd;
    }
    
    .content-wrapper code {
      background-color: #f4f4f4;
      padding: 2px 4px;
      border-radius: 3px;
      font-family: 'Courier New', monospace;
    }
    
    .content-wrapper pre {
      background-color: #f4f4f4;
      padding: 15px;
      border-radius: 5px;
      overflow-x: auto;
    }
    
    .content-wrapper pre code {
      background: none;
      padding: 0;
    }
    
         @media print {
       body {
         background-color: white;
         padding: 0;
       }
       
       .preview-container {
         box-shadow: none;
         border-radius: 0;
         padding: 20px;
       }
       
       .preview-header {
         margin-bottom: 10px;
         padding-bottom: 10px;
         border-bottom: 1px solid #ddd;
       }
       
       .print-button {
         display: none !important;
       }
       
       .usage-tips {
         display: none !important;
       }
       
       .preview-title {
         margin: 0 0 15px 0;
         font-size: 20px;
       }
     }
  </style>
</head>
<body>
    <div class="preview-container">
    <div class="preview-header">
      <h1 class="preview-title">${title}</h1>
      <button class="print-button print-button-pulse" id="printButton">
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M6 2h12v6H6z"/>
          <path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"/>
          <path d="M6 14h12v8H6z"/>
        </svg>
        点击保存PDF
      </button>
      
      <div class="usage-tips">
        <h3>📋 使用说明</h3>
        <ul>
          <li>✅ 您可以在此页面<span class="highlight">复制文字</span>和<span class="highlight">保存图片</span></li>
          <li>📄 点击右上角<span class="highlight">"点击保存PDF"</span>按钮调用浏览器打印功能</li>
          <li>🖨️ 在弹出的浏览器打印预览中，找到<span class="highlight">"目标"或"另存为"</span>选项</li>
          <li>🎨 <span style="color: #e74c3c; font-weight: bold;">重要：请勾选"显示背景图形"选项</span>以保证PDF样式完整</li>
          <li>💾 选择<span class="highlight">"另存为PDF"</span>，然后点击<span class="highlight">"保存"</span>按钮</li>
          <li>📁 选择保存位置，完成PDF文件下载</li>
          <li>🔗 原文链接：<a href="${window.location.href}" target="_blank" style="color: #007bff; text-decoration: none;">${window.location.href}</a></li>
          <li>⏰ 剪存时间：<span class="highlight">${new Date().toLocaleString('zh-CN')}</span></li>
          <li>✂️ 本文档由 <span class="highlight">飞书助手</span> 一键生成</li>
          <li>💖 觉得好用在chrome商店给个五星好评 <a href="https://chromewebstore.google.com/detail/%E9%A3%9E%E4%B9%A6%E5%8A%A9%E6%89%8B-%E9%A3%9E%E4%B9%A6lark%E5%AF%BC%E5%87%BA/cfenjfhlhjpkaaobmhbobajnnhifilbl" target="_blank" style="color: #007bff; text-decoration: none;">链接</a></li>
        </ul>
        <p style="margin: 10px 0 0 0; font-size: 13px; color: #6c757d;">
          💡 提示：如果找不到"另存为PDF"选项，请在打印设置中将打印机选择为"另存为PDF"或"Microsoft Print to PDF"
        </p>
        <p style="margin: 8px 0 0 0; font-size: 13px; color: #e74c3c; font-weight: 500;">
          🎨 特别提醒：务必勾选"显示背景图形"或"Background graphics"选项，否则PDF可能缺少样式和背景色
        </p>
      </div>
    </div>
    <div class="content-wrapper">
      ${bodyContent}
    </div>
  </div>
</body>
</html>`;
}

/**
 * 使用html2pdf.js生成PDF的HTML内容
 */
function createPdfHTML(bodyContent: string, title: string): string {
  // 确保内容不为空
  if (!bodyContent || !bodyContent.trim()) {
    bodyContent = '<p>文档内容为空</p>'
  }
  
  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${title || '未命名文档'}</title>
  <style>
    * {
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 20px;
      background-color: white;
      color: #333;
    }
    
    .pdf-container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 30px;
    }
    
    .pdf-title {
      font-size: 24px;
      font-weight: bold;
      color: #2c3e50;
      margin: 0 0 30px 0;
      text-align: center;
      padding-bottom: 20px;
      border-bottom: 2px solid #eee;
    }
    
    .content-wrapper {
      width: 100%;
    }
    
    /* 连续图片容器样式 */
    .content-wrapper .consecutive-images {
      display: flex !important;
      flex-wrap: nowrap !important;
      align-items: flex-start !important;
      gap: 10px !important;
      margin: 20px 0 10px 0 !important;
      overflow: hidden !important;
    }
    
    .content-wrapper .consecutive-images img {
      flex: 1 1 auto !important;
      min-width: 0 !important;
      max-width: none !important;
      width: auto !important;
      height: auto !important;
      object-fit: contain !important;
      margin: 0 !important;
      display: block !important;
    }
    
    .content-wrapper img {
      max-width: 100% !important;
      height: auto !important;
      object-fit: contain !important;
      page-break-inside: avoid !important;
      display: block !important;
      margin: 20px 0 10px 0 !important;
      box-sizing: border-box !important;
    }
    
    .content-wrapper h1, 
    .content-wrapper h2, 
    .content-wrapper h3, 
    .content-wrapper h4, 
    .content-wrapper h5, 
    .content-wrapper h6 {
      margin-top: 30px;
      margin-bottom: 15px;
      line-height: 1.4;
      page-break-after: avoid;
    }
    
    .content-wrapper p {
      margin: 15px 0;
    }
    
    .content-wrapper hr {
      margin: 15px 0;
      border: none;
      border-top: 1px solid #ddd;
      height: 1px;
    }
    
    .content-wrapper table {
      margin: 10px 0;
      border-collapse: collapse;
      width: 100%;
      page-break-inside: avoid;
    }
    
    .content-wrapper table th,
    .content-wrapper table td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    
    .content-wrapper table th {
      background-color: #f2f2f2;
      font-weight: bold;
    }
    
    .content-wrapper blockquote {
      margin: 10px 0;
      padding: 10px 15px;
      background-color: #f9f9f9;
      border-left: 4px solid #ddd;
    }
    
    .content-wrapper code {
      background-color: #f4f4f4;
      padding: 2px 4px;
      border-radius: 3px;
      font-family: 'Courier New', monospace;
    }
    
    .content-wrapper pre {
      background-color: #f4f4f4;
      padding: 15px;
      border-radius: 5px;
      overflow-x: auto;
      page-break-inside: avoid;
    }
    
    .content-wrapper pre code {
      background: none;
      padding: 0;
    }
  </style>
</head>
<body>
  <div class="pdf-container">
    <h1 class="pdf-title">${title}</h1>
    <div class="content-wrapper">
      ${bodyContent}
    </div>
  </div>
</body>
</html>`;
}

export async function exportPdfInjected() {
  // 获取head里面的name="title" 的meta 标签的content属性
  const title = document.querySelector('meta[name="title"]')?.getAttribute('content');
  const title2 = document.querySelector('title')?.textContent;
  const isPdfFile1 = title && title.endsWith('.pdf')
  const isPdfFile2 = title2 && title2.includes('.pdf')
  if (isPdfFile1 || isPdfFile2) {
    console.error('导出原pdf文档')
    await exportOriginalPDF()
    return
  }

  // 使用公共数据准备逻辑（图片预处理在这里完成）
  const data = await prepareExportData({ exportType: 'pdf', optimizeImages: true })
  if (!data) return

  const { recommendName, recoverScrollTop } = data

  // 在用户确认前预处理HTML内容
  Toast.loading({ content: '正在预处理文档内容', keepAlive: true, key: "preprocess" })

  // 使用新的HTML转换器直接转换，等待图片处理完成
  const htmlResult = await convertDocxToHtmlWithImages(docx.rootBlock, {
    useInlineStyles: true,        // PDF导出使用内联样式，避免样式丢失
    cssClassPrefix: 'feishu',     // 统一CSS类名前缀
    convertImages: true,          // 转换图片
    convertFiles: true            // 转换文件链接
  })
  console.error('🎉 使用新的HTML转换器转换完成！')
  console.error('- 转换器类型: 直接HTML转换 (替代 Markdown → HTML)')
  console.error('- 支持的格式: 粗体、斜体、删除线、链接、内联代码、数学公式')
  console.error('- HTML长度:', htmlResult.html.length)
  console.error('- 图片数量:', htmlResult.images.length)
  console.error('- 文件数量:', htmlResult.files.length)
  console.error('html result from new converter', htmlResult)

  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlResult.html;

  Toast.remove('preprocess')
  console.error('processed html length:', tempDiv.innerHTML.length)
  console.error('processed html preview:', tempDiv.innerHTML.substring(0, 200))
  
  // 检查内容是否为空
  if (!tempDiv.innerHTML.trim()) {
    Toast.error({ content: '文档内容为空，无法生成PDF' })
    recoverScrollTop?.()
    return
  }

  const filename = `${recommendName}.pdf`

  const toBlobContent = async (): Promise<Blob> => {
    // 第一阶段：准备HTML内容
    Toast.loading({
      content: '正在准备PDF文档内容（1/3）',
      keepAlive: true,
      key: 'DOWNLOADING',
    })

    // 创建用于PDF生成的HTML内容
    const pdfHtml = createPdfHTML(tempDiv.innerHTML, recommendName)
    console.error('PDF HTML内容长度:', pdfHtml.length)
    console.error('PDF HTML前500字符:', pdfHtml.substring(0, 500))

    // 第二阶段：动态导入html2pdf.js
    Toast.loading({
      content: '正在加载PDF生成器（2/3）',
      keepAlive: true,
      key: 'DOWNLOADING',
    })

    const html2pdf = await import('html2pdf.js')

    // 第三阶段：生成PDF
    Toast.loading({
      content: '正在生成PDF文件（3/3）',
      keepAlive: true,
      key: 'DOWNLOADING',
    })

    // 创建临时div来渲染HTML
    const tempContainer = document.createElement('div')
    tempContainer.innerHTML = pdfHtml
    tempContainer.style.position = 'absolute'
    tempContainer.style.left = '-9999px'
    tempContainer.style.top = '-9999px'
    tempContainer.style.width = '800px'
    tempContainer.style.minHeight = '100px'
    document.body.appendChild(tempContainer)

    try {
      // 等待DOM渲染完成
      await new Promise(resolve => setTimeout(resolve, 100))

      // 检查内容是否正确加载
      const targetElement = tempContainer.querySelector('.pdf-container')
      if (!targetElement) {
        throw new Error('PDF容器元素未找到')
      }
      
      console.error('目标元素内容长度:', targetElement.innerHTML.length)
      console.error('目标元素前200字符:', targetElement.innerHTML.substring(0, 200))

      // 配置html2pdf选项
      const opt = {
        margin: [10, 10, 10, 10], // 上右下左边距 (mm)
        filename: filename,
        image: { 
          type: 'jpeg', 
          quality: 0.8 
        },
        html2canvas: { 
          scale: 1.5,
          useCORS: true,
          allowTaint: true,
          scrollX: 0,
          scrollY: 0,
          width: 800,
          height: null,
          logging: true
        },
        jsPDF: { 
          unit: 'mm', 
          format: 'a4', 
          orientation: 'portrait' 
        },
        pagebreak: { mode: ['avoid-all', 'css', 'legacy'] }
      }

      console.error('开始生成PDF...')
      
      // 生成PDF blob - 使用正确的元素选择
      const pdfBlob = await (html2pdf as any).default().set(opt).from(targetElement).outputPdf('blob')
      
      console.error('PDF生成完成，大小:', pdfBlob.size, 'bytes')
      
      if (pdfBlob.size < 5000) {
        console.error('警告：PDF文件过小，可能生成失败')
      }
      
      return pdfBlob
    } finally {
      // 清理临时元素
      if (document.body.contains(tempContainer)) {
        document.body.removeChild(tempContainer)
      }
    }
  }

  // 使用公共下载逻辑
  await downloadWithFileSave(
    toBlobContent,
    filename,
    '.pdf',
    '是否继续导出PDF文件？',
    recoverScrollTop
  )

  // **可选：如果用户仍然想要预览功能，可以在这里添加**
  // 注释掉下面的代码，因为现在直接下载PDF了
  /*
  try {
    const fullHtml = createPreviewHTML(tempDiv.innerHTML, recommendName)
    const blob = new Blob([fullHtml], { type: 'text/html;charset=utf-8' })
    const url = URL.createObjectURL(blob)

    const newWindow = window.open(url, '_blank')
    if (newWindow) {
      // 在新窗口加载完成后添加打印功能
      newWindow.addEventListener('load', () => {
        try {
          // 直接在新窗口中添加事件监听器
          const printButton = newWindow.document.getElementById('printButton')
          if (printButton) {
            printButton.addEventListener('click', () => {
              // 移除脉动效果（只移除一次）
              printButton.classList.remove('print-button-pulse')

              // 直接调用打印功能，不改变按钮状态
              newWindow.print()
            })
          }
        } catch (e) {
          console.error('添加打印事件失败:', e)
        }

        setTimeout(() => {
          URL.revokeObjectURL(url)
        }, 1000)
      })
      Toast.success({ content: '✅ PDF预览已准备就绪！请按照页面说明操作保存PDF文件', duration: 4000 })
    } else {
      Toast.error({ content: '无法打开新页面，请检查浏览器弹窗设置' })
    }

    // 恢复页面滚动位置
    recoverScrollTop?.()
  } catch (error) {
    console.error('打开预览页面失败:', error)
    Toast.error({ content: '打开预览页面失败' })
    // 发生错误时也要恢复滚动位置
    recoverScrollTop?.()
  }
  */
}



