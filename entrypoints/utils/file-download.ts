import { confirmWithCancel } from '@/pkg/utils/notification'
import { fileSave, supported } from 'browser-fs-access'
import { Toast } from "@/pkg/lark/env"

// 通用的下载逻辑
export const downloadWithFileSave = async (
  toBlobContent: () => Promise<Blob>,
  filename: string,
  extension: string,
  confirmMessage: string,
  recoverScrollTop?: () => void
) => {
  if (!supported) {
    Toast.error({ content: '当前浏览器不支持文件保存功能，请使用现代浏览器' })
    recoverScrollTop?.()
    return
  }

  // 检查用户激活状态
  if (!navigator.userActivation?.isActive) {
    // 用户激活状态失效时，先获取用户确认来重新激活
    const confirmed = await confirmWithCancel(confirmMessage)
    if (!confirmed) {
      recoverScrollTop?.()
      return
    }
  }

  try {
    // 生成文件内容
    const blob = await toBlobContent()

    // 移除所有loading提示
    Toast.remove('DOWNLOADING')

    // 使用现代API保存文件
    await fileSave(blob, {
      fileName: filename,
      extensions: [extension],
    })

    Toast.success({ content: '导出成功' })
  } catch (error) {
    console.error('文件保存失败:', error)
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    
    if (errorMessage.includes('user gesture') || errorMessage.includes('User activation')) {
      Toast.error({ 
        content: '文件保存需要用户操作，请重新点击导出按钮' 
      })
    } else {
      Toast.error({ 
        content: `文件保存失败: ${errorMessage}` 
      })
    }
  } finally {
    recoverScrollTop?.()
  }
} 