import { exportToPDFAll } from "./export-pdf/pdf-export-batch";


export default defineContentScript({
  matches: [
    "https://*.feishu.cn/*",
    "https://*.feishu.net/*",
    "https://*.larksuite.com/*",
    "https://*.feishu-pre.net/*",
    "https://*.larkoffice.com/*",
  ],
  runAt: "document_end",
  async main() {
    browser.runtime.onMessage.addListener(async (message) => {
      if (message.action === "exportPdfAll") {
        try {
          await exportToPDFAll();
          return { success: true };
        } catch (error) {
          console.error('导出PDF失败:', error);
          return { success: false, error: error instanceof Error ? error.message : String(error) };
        }
      }
    });
  },
});
