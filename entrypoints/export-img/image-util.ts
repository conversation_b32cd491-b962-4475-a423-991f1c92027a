import { markdownToHtml } from "@/pkg/utils/markdown-to-html";
import { Docx } from "@/pkg/lark/docx";
import { convertDocxToHtmlWithImages } from "@/pkg/lark/html-converter-fn";
import { Toast } from "@/pkg/lark/env";
import { downloadWithFileSave } from "../utils/file-download";
import { prepareExportData } from "../export-pdf/prepare-util";
import { docx } from "@/pkg/lark/docx";

export async function exportImageInjected() {
  // 使用公共数据准备逻辑（图片预处理在这里完成）
  const data = await prepareExportData({ exportType: 'image', optimizeImages: true })
  if (!data) return

  const { root, images, recommendName, recoverScrollTop } = data

  // 在用户确认前预处理HTML内容
  Toast.loading({ content: '正在预处理文档内容', keepAlive: true, key: "preprocess" })

  // 使用新的HTML转换器直接转换，等待图片处理完成
  const htmlResult = await convertDocxToHtmlWithImages(docx.rootBlock, {
    useInlineStyles: true,        // 图片导出使用内联样式，确保样式完整
    cssClassPrefix: 'feishu',     // 统一CSS类名前缀
    convertImages: true,          // 转换图片
    convertFiles: true            // 转换文件链接
  })
  const html = htmlResult.html
  console.error('🎉 图片导出使用新的HTML转换器转换完成！')
  console.error('html from new converter', html, typeof html)

  // 创建临时DOM元素用于渲染
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = html

  // 直接修改HTML中的所有img元素的样式
  const imgElements = tempDiv.querySelectorAll('img')
  imgElements.forEach(img => {
    img.style.cssText = `
      max-width: 100% !important;
      height: auto !important;
      object-fit: contain !important;
      display: block !important;
      margin: 20px 0 10px 0 !important;
      box-sizing: border-box !important;
    `
  })

  // 添加CSS样式优化其他元素布局，限制作用域到tempDiv内部
  const style = document.createElement('style');
  style.textContent = `
    .temp-export-container * {
      box-sizing: border-box !important;
    }
    .temp-export-container p, 
    .temp-export-container div, 
    .temp-export-container span, 
    .temp-export-container h1, 
    .temp-export-container h2, 
    .temp-export-container h3, 
    .temp-export-container h4, 
    .temp-export-container h5, 
    .temp-export-container h6 {
      max-width: 100% !important;
      word-wrap: break-word !important;
    }
  `;
  tempDiv.appendChild(style);

  // 给tempDiv添加特定的class，确保样式只作用于此容器
  tempDiv.className = 'temp-export-container';

  // 设置临时div的样式，确保有合适的尺寸
  tempDiv.style.cssText = `
    position: absolute;
    top: -9999px;
    left: -9999px;
    max-width: 500px;
    width: 100%;
    padding: 40px;
    background: white;
    line-height: 1.6;
    color: #333;
    z-index: -1;
  `
  // 添加到body中进行渲染
  document.body.appendChild(tempDiv)

  Toast.remove('preprocess')
  console.error('tempDiv', tempDiv)

  const filename = `${recommendName}.png`

  const toBlobContent = async () => {
    // 第一阶段：准备渲染
    Toast.loading({
      content: '正在准备图片渲染（1/2）',
      keepAlive: true,
      key: 'DOWNLOADING',
    })

    // 等待DOM更新
    await new Promise(resolve => setTimeout(resolve, 100))

    try {
      // 第二阶段：生成图片
      Toast.loading({
        content: '正在生成图片文件（2/2）',
        keepAlive: true,
        key: 'DOWNLOADING',
      })

      // 动态导入html2canvas避免构建时错误
      const html2canvas = (await import('html2canvas')).default

      // 使用html2canvas生成图片
      const canvas = await html2canvas(tempDiv, {
        scale: 2, // 提高清晰度
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: 800, // 设置最大宽度为500px
        windowWidth: 800,
        scrollX: 0,
        scrollY: 0,
        removeContainer: false,
        logging: true // 开启日志以便调试
      })

      // 将canvas转换为blob
      return new Promise<Blob>((resolve) => {
        canvas.toBlob((blob: Blob | null) => {
          resolve(blob!)
        }, 'image/png', 1.0)
      })
    } finally {
      // 清理临时元素
      if (tempDiv.parentNode) {
        document.body.removeChild(tempDiv)
      }
    }
  }

  // 使用公共下载逻辑
  await downloadWithFileSave(
    toBlobContent,
    filename,
    '.png',
    '是否继续导出图片文件？',
    recoverScrollTop
  )
} 