/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 240px;
}

#app {
  width: 500px;
  background: white;
  overflow: hidden;
}

/* 头部样式 */
.header {
  background: #667eea;
  color: white;
  padding: 16px;
  text-align: center;
}

.logo {
  font-size: 18px;
  margin-bottom: 6px;
}

.header h2 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 3px;
}

.subtitle {
  font-size: 11px;
  opacity: 0.9;
}

/* 内容区域 */
.content {
  padding: 16px;
}

/* 导出网格 */
.export-grid {
  display: grid;
  gap: 8px;
}

/* 导出按钮 */
.export-btn {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: border-color 0.2s;
}

.export-btn:hover {
  border-color: #dee2e6;
}

/* 按钮图标 */
.btn-icon {
  font-size: 16px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 4px;
}

/* 按钮文本 */
.btn-text {
  flex: 1;
}

.btn-title {
  font-size: 13px;
  font-weight: 600;
  color: #2d3748;
  display: block;
}

.btn-desc {
  font-size: 10px;
  color: #718096;
  margin-top: 1px;
}

/* 状态区域 */
.status-section {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #e9ecef;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 5px;
  justify-content: center;
}

.status-dot {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background: #28a745;
}

.status-text {
  font-size: 10px;
  color: #718096;
}

/* 底部样式 */
.footer {
  background: #f8f9fa;
  padding: 10px 16px;
  text-align: center;
  border-top: 1px solid #e9ecef;
}

.social-info {
  font-size: 10px;
  color: #6c757d;
}

.social-icon {
  margin-right: 3px;
}

/* 响应式调整 */
@media (max-width: 380px) {
  #app {
    width: 100%;
    margin: 0 8px;
  }
  
  .export-btn {
    padding: 14px;
  }
  
  .btn-icon {
    width: 36px;
    height: 36px;
    font-size: 20px;
  }
}

/* 优化性能的样式 */
.export-btn,
.header,
#app {
  will-change: transform;
}

/* 减少重绘 */
.export-btn::before,
.header::before {
  will-change: left;
}

 

 

 