import './style.css';

// 更新状态指示器
const updateStatus = (message: string, color: string = '#28a745'): void => {
  const statusDot = document.querySelector('.status-dot') as HTMLElement;
  const statusText = document.querySelector('.status-text') as HTMLElement;
  
  if (statusDot && statusText) {
    statusDot.style.background = color;
    statusText.textContent = message;
  }
};

// 发送消息到后台脚本
const sendMessage = async (action: string): Promise<void> => {
  try {
    updateStatus('导出中...', '#ffc107');
    await browser.runtime.sendMessage({ action });
    updateStatus('导出成功', '#28a745');
    
    setTimeout(() => {
      window.close();
    }, 500);
    
  } catch (error) {
    console.error('导出失败:', error);
    updateStatus('导出失败', '#dc3545');
    
    setTimeout(() => {
      updateStatus('就绪', '#28a745');
    }, 2000);
  }
};

// 导出处理函数
const handleExportPdf = () => sendMessage("exportPdf");
const handleExportWord = () => sendMessage("exportWord");
const handleExportMd = () => sendMessage("exportMd");

// 绑定事件监听器
const bindEventListeners = (): void => {
  document.getElementById('exportPdf')?.addEventListener('click', handleExportPdf);
  document.getElementById('exportWord')?.addEventListener('click', handleExportWord);
  document.getElementById('exportMd')?.addEventListener('click', handleExportMd);
};

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
  bindEventListeners();
  updateStatus('就绪');
});