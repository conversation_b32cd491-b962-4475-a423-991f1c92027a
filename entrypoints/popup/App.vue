<script lang="ts" setup>
import { onMounted } from 'vue';

// 发送消息到后台脚本
const sendMessage = async (action: string): Promise<void> => {
  try {
    await browser.runtime.sendMessage({ action });
    
    setTimeout(() => {
      window.close();
    }, 500);
    
  } catch (error) {
    console.error('导出失败:', error);
  }
};

// 导出处理函数
const handleExportPdf = () => sendMessage("exportPdf");
const handleExportWord = () => sendMessage("exportWord");
const handleExportMd = () => sendMessage("exportMd");
</script>

<template>
  <div class="w-96 bg-white">
    <!-- Header -->
    <div class="px-6 py-6">
      <div class="flex items-center justify-center">
        <div class="w-10 h-10 bg-black rounded-full flex items-center justify-center">
          <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3M3 17V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
          </svg>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="px-6 pb-6">
      <div class="grid grid-cols-3 gap-3">
        <!-- PDF Button -->
        <button 
          @click="handleExportPdf"
          class="flex flex-col items-center p-4 border border-gray-200 rounded-xl hover:border-red-200 hover:bg-red-50 transition-all duration-200 group"
        >
          <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-3 group-hover:bg-red-200 transition-colors">
            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <span class="text-sm font-medium text-gray-900">PDF</span>
          <span class="text-xs text-gray-500 mt-1">Portable</span>
        </button>

        <!-- Word Button -->
        <button 
          @click="handleExportWord"
          class="flex flex-col items-center p-4 border border-gray-200 rounded-xl hover:border-blue-200 hover:bg-blue-50 transition-all duration-200 group"
        >
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-3 group-hover:bg-blue-200 transition-colors">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </div>
          <span class="text-sm font-medium text-gray-900">Word</span>
          <span class="text-xs text-gray-500 mt-1">Editable</span>
        </button>

        <!-- Markdown Button -->
        <button 
          @click="handleExportMd"
          class="flex flex-col items-center p-4 border border-gray-200 rounded-xl hover:border-green-200 hover:bg-green-50 transition-all duration-200 group"
        >
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-3 group-hover:bg-green-200 transition-colors">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
          </div>
          <span class="text-sm font-medium text-gray-900">Markdown</span>
          <span class="text-xs text-gray-500 mt-1">Lightweight</span>
        </button>
      </div>
    </div>

    <!-- Footer -->
    <div class="px-6 py-4 border-t border-gray-100 bg-gray-50">
      <div class="flex items-center justify-center gap-2 text-xs text-gray-500">
        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-2-2V10a2 2 0 012-2h2m2-4h6a2 2 0 012 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2V6a2 2 0 012-2z" />
        </svg>
        <span>QQ: 741683982</span>
      </div>
    </div>
  </div>
</template>

 