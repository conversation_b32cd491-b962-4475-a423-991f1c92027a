<script lang="ts" setup>
import { ref, onMounted } from 'vue';

const isLoading = ref(false);
const activeButton = ref('');

// 发送消息到后台脚本
const sendMessage = async (action: string): Promise<void> => {
  try {
    isLoading.value = true;
    activeButton.value = action;

    await browser.runtime.sendMessage({ action });

    // 添加成功动画延迟
    setTimeout(() => {
      window.close();
    }, 2000);

  } catch (error) {
    console.error('导出失败:', error);
    isLoading.value = false;
    activeButton.value = '';
  }
};

// 导出处理函数
const handleExportPdf = () => sendMessage("exportPdf");
const handleExportWord = () => sendMessage("exportWord");
const handleExportMd = () => sendMessage("exportMd");

// 添加震动效果
const triggerHaptic = () => {
  if (navigator.vibrate) {
    navigator.vibrate(50);
  }
};
</script>

<template>
  <div class="export-popup">
    <!-- 动态背景 -->
    <div class="background-animation">
      <div class="gradient-orb orb-1"></div>
      <div class="gradient-orb orb-2"></div>
      <div class="gradient-orb orb-3"></div>
    </div>

    <!-- 主容器 -->
    <div class="popup-container">
      <!-- Header -->
      <div class="header-section">
        <div class="logo-container">
          <div class="logo-ring">
            <div class="logo-inner">
              <svg class="logo-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
          </div>
        </div>
        <h1 class="title">Export Wizard</h1>
        <p class="subtitle">Transform your content instantly</p>
      </div>

      <!-- Export Buttons -->
      <div class="buttons-grid">
        <!-- PDF Button -->
        <button
          @click="handleExportPdf(); triggerHaptic()"
          :class="['export-btn', 'pdf-btn', {
            'active': activeButton === 'exportPdf',
            'loading': isLoading && activeButton === 'exportPdf'
          }]"
          :disabled="isLoading"
        >
          <div class="btn-bg"></div>
          <div class="btn-content">
            <div class="icon-container">
              <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <div class="loading-spinner" v-if="isLoading && activeButton === 'exportPdf'">
                <div class="spinner"></div>
              </div>
            </div>
            <div class="btn-text">
              <span class="btn-title">PDF</span>
              <span class="btn-desc">Universal Format</span>
            </div>
          </div>
          <div class="btn-glow"></div>
        </button>

        <!-- Word Button -->
        <button
          @click="handleExportWord(); triggerHaptic()"
          :class="['export-btn', 'word-btn', {
            'active': activeButton === 'exportWord',
            'loading': isLoading && activeButton === 'exportWord'
          }]"
          :disabled="isLoading"
        >
          <div class="btn-bg"></div>
          <div class="btn-content">
            <div class="icon-container">
              <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              <div class="loading-spinner" v-if="isLoading && activeButton === 'exportWord'">
                <div class="spinner"></div>
              </div>
            </div>
            <div class="btn-text">
              <span class="btn-title">DOCX</span>
              <span class="btn-desc">Editable Document</span>
            </div>
          </div>
          <div class="btn-glow"></div>
        </button>

        <!-- Markdown Button -->
        <button
          @click="handleExportMd(); triggerHaptic()"
          :class="['export-btn', 'md-btn', {
            'active': activeButton === 'exportMd',
            'loading': isLoading && activeButton === 'exportMd'
          }]"
          :disabled="isLoading"
        >
          <div class="btn-bg"></div>
          <div class="btn-content">
            <div class="icon-container">
              <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
              <div class="loading-spinner" v-if="isLoading && activeButton === 'exportMd'">
                <div class="spinner"></div>
              </div>
            </div>
            <div class="btn-text">
              <span class="btn-title">MD</span>
              <span class="btn-desc">Lightweight Markup</span>
            </div>
          </div>
          <div class="btn-glow"></div>
        </button>
      </div>

      <!-- Footer -->
      <div class="footer-section">
        <div class="contact-info">
          <div class="contact-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </div>
          <span class="contact-text">Support: QQ 741683982</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 主容器样式 */
.export-popup {
  width: 420px;
  height: 580px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
   box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* 动态背景 */
.background-animation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  opacity: 0.7;
  animation: float 6s ease-in-out infinite;
}

.orb-1 {
  width: 200px;
  height: 200px;
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  top: -50px;
  left: -50px;
  animation-delay: 0s;
}

.orb-2 {
  width: 150px;
  height: 150px;
  background: linear-gradient(45deg, #48cae4, #023e8a);
  bottom: -30px;
  right: -30px;
  animation-delay: 2s;
}

.orb-3 {
  width: 120px;
  height: 120px;
  background: linear-gradient(45deg, #06ffa5, #00d4aa);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(120deg); }
  66% { transform: translateY(10px) rotate(240deg); }
}

/* 主容器 */
.popup-container {
  position: relative;
  z-index: 10;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 32px 24px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
}

/* Header 样式 */
.header-section {
  text-align: center;
  margin-bottom: 40px;
}

.logo-container {
  margin-bottom: 20px;
}

.logo-ring {
  width: 80px;
  height: 80px;
  margin: 0 auto;
  position: relative;
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  border-radius: 50%;
  padding: 3px;
  animation: pulse-ring 2s ease-in-out infinite;
}

.logo-inner {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.logo-icon {
  width: 32px;
  height: 32px;
  color: #667eea;
  stroke-width: 2.5;
}

@keyframes pulse-ring {
  0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7); }
  50% { transform: scale(1.05); box-shadow: 0 0 0 10px rgba(255, 107, 107, 0); }
  100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(255, 107, 107, 0); }
}

.title {
  font-size: 28px;
  font-weight: 800;
  color: white;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.5px;
}

.subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-weight: 500;
}

/* 按钮网格 */
.buttons-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
  justify-content: center;
}

/* 导出按钮基础样式 */
.export-btn {
  position: relative;
  height: 80px;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
  background: transparent;
}

.export-btn:hover {
  transform: translateY(-4px);
}

.export-btn:active {
  transform: translateY(-2px) scale(0.98);
}

.export-btn:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

/* 按钮背景 */
.btn-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 20px;
  transition: all 0.3s ease;
}

/* 按钮发光效果 */
.btn-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 22px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.export-btn:hover .btn-glow {
  opacity: 1;
}

/* PDF 按钮样式 */
.pdf-btn .btn-bg {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  box-shadow: 0 8px 32px rgba(255, 107, 107, 0.3);
}

.pdf-btn .btn-glow {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  filter: blur(8px);
}

.pdf-btn:hover .btn-bg {
  background: linear-gradient(135deg, #ff5252 0%, #e53935 100%);
  box-shadow: 0 12px 40px rgba(255, 107, 107, 0.4);
}

/* Word 按钮样式 */
.word-btn .btn-bg {
  background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
  box-shadow: 0 8px 32px rgba(79, 195, 247, 0.3);
}

.word-btn .btn-glow {
  background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
  filter: blur(8px);
}

.word-btn:hover .btn-bg {
  background: linear-gradient(135deg, #29b6f6 0%, #0288d1 100%);
  box-shadow: 0 12px 40px rgba(79, 195, 247, 0.4);
}

/* Markdown 按钮样式 */
.md-btn .btn-bg {
  background: linear-gradient(135deg, #66bb6a 0%, #43a047 100%);
  box-shadow: 0 8px 32px rgba(102, 187, 106, 0.3);
}

.md-btn .btn-glow {
  background: linear-gradient(135deg, #66bb6a 0%, #43a047 100%);
  filter: blur(8px);
}

.md-btn:hover .btn-bg {
  background: linear-gradient(135deg, #43a047 0%, #388e3c 100%);
  box-shadow: 0 12px 40px rgba(102, 187, 106, 0.4);
}

/* 按钮内容 */
.btn-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 24px;
  color: white;
}

.icon-container {
  position: relative;
  width: 48px;
  height: 48px;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.btn-icon {
  width: 24px;
  height: 24px;
  stroke-width: 2.5;
  transition: transform 0.3s ease;
}

.export-btn:hover .btn-icon {
  transform: scale(1.1);
}

.btn-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.btn-title {
  font-size: 18px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.btn-desc {
  font-size: 12px;
  opacity: 0.9;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 加载状态 */
.loading-spinner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(5px);
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 激活状态 */
.export-btn.active {
  transform: translateY(-2px) scale(1.02);
}

.export-btn.active .btn-bg {
  animation: pulse-success 0.6s ease-in-out;
}

@keyframes pulse-success {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Footer 样式 */
.footer-section {
  margin-top: auto;
  padding-top: 24px;
}

.contact-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.contact-icon {
  width: 16px;
  height: 16px;
  color: rgba(255, 255, 255, 0.8);
}

.contact-icon svg {
  width: 100%;
  height: 100%;
  stroke-width: 2;
}

.contact-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* 响应式调整 */
@media (max-height: 600px) {
  .export-popup {
    height: auto;
    min-height: 500px;
  }

  .popup-container {
    padding: 24px 20px;
  }

  .header-section {
    margin-bottom: 32px;
  }

  .logo-ring {
    width: 64px;
    height: 64px;
  }

  .logo-icon {
    width: 24px;
    height: 24px;
  }

  .title {
    font-size: 24px;
  }

  .export-btn {
    height: 70px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .logo-inner {
    background: rgba(0, 0, 0, 0.8);
  }

  .logo-icon {
    color: #8b5cf6;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .export-popup {
    border: 2px solid white;
  }

  .btn-bg {
    border: 1px solid rgba(255, 255, 255, 0.5);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .gradient-orb {
    animation: none;
  }

  .logo-ring {
    animation: none;
  }
}
</style>

 