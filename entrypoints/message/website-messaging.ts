import { defineWindowMessaging } from '@webext-core/messaging/page';

export interface WebsiteMessengerSchema {
  exportPdf(data: unknown): void;
  exportImage(data: unknown): void;
  handleExportPdfAll(data: unknown): void;
  somethingHappened(data: unknown): void;
  exportPdfAll(data: unknown): void;
}

export const websiteMessenger = defineWindowMessaging<WebsiteMessengerSchema>({
  namespace: 'website-messaging',
}); 