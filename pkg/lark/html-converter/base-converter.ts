import { Blocks, BlockType, Operation, Attributes } from '../docx'

export interface ConversionContext {
  images: HTMLImageElement[]
  files: HTMLAnchorElement[]
  options: HtmlTransformerOptions
  parentConverter?: BlockConverter
  transformer?: BlockTransformer
}

export interface HtmlTransformerOptions {
  /**
   * 是否使用内联样式
   * @default true
   */
  useInlineStyles?: boolean
  /**
   * CSS类名前缀
   * @default 'feishu'
   */
  cssClassPrefix?: string
  /**
   * 自定义样式
   */
  customStyles?: Record<string, string>
  /**
   * 是否转换图片
   * @default true
   */
  convertImages?: boolean
  /**
   * 是否转换文件
   * @default true
   */
  convertFiles?: boolean
}

export interface HtmlTransformResult {
  html: string
  images: HTMLImageElement[]
  files: HTMLAnchorElement[]
  styles?: string
}

export interface HtmlAttributes extends Attributes {
  color?: string
  backgroundColor?: string
  underline?: string
  fontSize?: string
}

export interface BlockTransformer {
  convertBlock(block: Blocks, context: ConversionContext): string
}

export abstract class BlockConverter {
  /**
   * 检查是否可以处理指定的块类型
   */
  abstract canHandle(blockType: BlockType): boolean

  /**
   * 转换块为HTML
   */
  abstract convert(block: Blocks, context: ConversionContext): string

  /**
   * 转换文本操作为HTML
   */
  protected convertOperationsToHtml(ops: Operation[] = [], context: ConversionContext): string {
    if (!ops.length) return ''

    return ops.map(op => this.convertOperationToHtml(op, context)).join('')
  }

  /**
   * 转换单个操作为HTML
   */
  protected convertOperationToHtml(op: Operation, context: ConversionContext): string {
    const { attributes, insert } = op

    if (!insert) return ''

    // 处理特殊字符转义
    let content = this.escapeHtml(insert)

    // 处理内联代码
    if (attributes?.inlineCode) {
      return `<code>${content}</code>`
    }

    // 处理数学公式
    if (attributes?.equation) {
      const equation = attributes.equation.replace(/\n$/, '')
      return `<span class="equation">${this.escapeHtml(equation)}</span>`
    }

    // 应用文本格式
    content = this.applyTextFormats(content, attributes, context)

    return content
  }

  /**
   * 应用文本格式
   */
  protected applyTextFormats(content: string, attributes?: Attributes, context?: ConversionContext): string {
    if (!attributes) return content

    let html = content

    // 粗体
    if (attributes.bold) {
      html = `<strong>${html}</strong>`
    }

    // 斜体
    if (attributes.italic) {
      html = `<em>${html}</em>`
    }

    // 删除线
    if (attributes.strikethrough) {
      html = `<del>${html}</del>`
    }

    // 下划线 (HTML特有)
    if ((attributes as HtmlAttributes).underline) {
      html = `<u>${html}</u>`
    }

    // 链接
    if (attributes.link) {
      const url = decodeURIComponent(attributes.link)
      html = `<a href="${this.escapeHtml(url)}">${html}</a>`
    }

    // 颜色和背景色 (HTML特有)
    const htmlAttrs = attributes as HtmlAttributes
    const textHighlight = attributes.textHighlightBackground as string
    const textColor = attributes.textHighlight as string

    if (htmlAttrs.color || htmlAttrs.backgroundColor || textHighlight || textColor || htmlAttrs.fontSize) {
      const styles: string[] = []

      if (htmlAttrs.color) {
        styles.push(`color: ${htmlAttrs.color}`)
      }

      // 支持 textHighlight 属性作为文字颜色
      if (textColor) {
        styles.push(`color: ${textColor}`)
      }

      if (htmlAttrs.backgroundColor) {
        styles.push(`background-color: ${htmlAttrs.backgroundColor}`)
      }

      // 支持 textHighlightBackground 属性
      if (textHighlight) {
        styles.push(`background-color: ${textHighlight}`)
      }

      if (htmlAttrs.fontSize) {
        styles.push(`font-size: ${htmlAttrs.fontSize}`)
      }

      if (styles.length > 0) {
        html = `<span style="${styles.join('; ')}">${html}</span>`
      }
    }

    return html
  }

  /**
   * HTML转义
   */
  protected escapeHtml(text: string): string {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  /**
   * 生成CSS类名
   */
  protected getClassName(suffix: string, context: ConversionContext): string {
    const prefix = context.options.cssClassPrefix || 'feishu'
    return `${prefix}-${suffix}`
  }

  /**
   * 获取自定义样式
   */
  protected getCustomStyle(key: string, context: ConversionContext): string | undefined {
    return context.options.customStyles?.[key]
  }

  /**
   * 通用的子块转换方法
   * 利用context中的transformer来转换任意类型的子块
   */
  protected convertChildBlocks(children: Blocks[], context: ConversionContext): string[] {
    if (!children || children.length === 0) {
      return []
    }

    const childrenHtml: string[] = []
    
    for (const child of children) {
      const childHtml = this.convertChildBlock(child, context)
      if (childHtml && childHtml.trim()) {
        childrenHtml.push(childHtml)
      }
    }

    return childrenHtml
  }

  /**
   * 转换单个子块
   */
  protected convertChildBlock(block: Blocks, context: ConversionContext): string {
    // 如果有transformer，使用它来转换子块
    if (context.transformer) {
      return context.transformer.convertBlock(block, {
        ...context,
        parentConverter: this
      })
    }

    // 降级处理：如果没有transformer，尝试简单的文本提取
    return this.extractBlockContent(block, context)
  }

  /**
   * 分组连续的同类型列表块
   */
  protected groupConsecutiveListBlocks(children: Blocks[]): Array<{ type: 'list' | 'single', blocks: Blocks[], listType?: BlockType }> {
    const groups: Array<{ type: 'list' | 'single', blocks: Blocks[], listType?: BlockType }> = []
    let currentListGroup: Blocks[] = []
    let currentListType: BlockType | undefined = undefined

    const isListType = (blockType: BlockType): boolean => 
      blockType === BlockType.BULLET || 
      blockType === BlockType.ORDERED || 
      blockType === BlockType.TODO

    for (const child of children) {
      if (isListType(child.type)) {
        if (currentListType !== child.type) {
          // 新的列表类型，先保存之前的分组
          if (currentListGroup.length > 0) {
            groups.push({ type: 'list', blocks: currentListGroup, listType: currentListType })
          }
          currentListGroup = [child]
          currentListType = child.type
        } else {
          // 相同的列表类型，添加到当前分组
          currentListGroup.push(child)
        }
      } else {
        // 非列表类型，先保存之前的列表分组
        if (currentListGroup.length > 0) {
          groups.push({ type: 'list', blocks: currentListGroup, listType: currentListType })
          currentListGroup = []
          currentListType = undefined
        }
        // 单个非列表块
        groups.push({ type: 'single', blocks: [child] })
      }
    }

    // 处理最后的列表分组
    if (currentListGroup.length > 0) {
      groups.push({ type: 'list', blocks: currentListGroup, listType: currentListType })
    }

    return groups
  }

  /**
   * 将分组的列表块转换为HTML列表
   */
  protected convertListGroup(listBlocks: Blocks[], listType: BlockType, context: ConversionContext): string {
    if (!listBlocks.length) return ''

    const listItems = listBlocks.map(block => this.convertChildBlock(block, context)).filter(Boolean)
    if (!listItems.length) return ''

    const listStyle = this.getListStyle(listType, context)
    const listTag = this.getListTag(listType)

    // 为有序列表和无序列表添加标记颜色样式
    if (context.options.useInlineStyles !== false && (listType === BlockType.ORDERED || listType === BlockType.BULLET)) {
      const markerStyle = 'li::marker { color: #0084ff; }'
      return `<style>${markerStyle}</style><${listTag} style="${listStyle}">${listItems.join('')}</${listTag}>`
    }

    return `<${listTag} style="${listStyle}">${listItems.join('')}</${listTag}>`
  }

  /**
   * 获取列表标签
   */
  private getListTag(listType: BlockType): string {
    return listType === BlockType.ORDERED ? 'ol' : 'ul'
  }

  /**
   * 获取列表样式
   */
  private getListStyle(listType: BlockType, context: ConversionContext): string {
    if (context.options.useInlineStyles === false) {
      return ''
    }

    switch (listType) {
      case BlockType.TODO:
        return 'list-style: none; padding-left: 0;margin: 0;'
      case BlockType.ORDERED:
      case BlockType.BULLET:
        return 'padding-left: 24px; margin: 0;'
      default:
        return 'padding-left: 24px; margin: 0;'
    }
  }

  /**
   * 简单的块内容提取（降级处理）
   */
  private extractBlockContent(block: Blocks, context: ConversionContext): string {
    // 处理文本类型
    if (block.type === BlockType.TEXT ||
        block.type === BlockType.HEADING7 ||
        block.type === BlockType.HEADING8 ||
        block.type === BlockType.HEADING9) {
      const ops = block.zoneState?.content?.ops || []
      const content = this.convertOperationsToHtml(ops, context)
      return content ? `<div>${content}</div>` : ''
    }

    // 对于其他类型，提取文本内容
    const textContent = block.zoneState?.allText || ''
    return textContent ? `<div>${this.escapeHtml(textContent)}</div>` : ''
  }
} 