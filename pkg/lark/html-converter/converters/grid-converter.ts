import { BlockConverter, ConversionContext } from '../base-converter'
import { BlockType, Blocks } from '../../docx'
import { imageCache } from './image-cache'

export class GridConverter extends BlockConverter {
  canHandle(blockType: BlockType): boolean {
    return blockType === BlockType.GRID || blockType === BlockType.GRID_COLUMN
  }

  convert(block: Blocks, context: ConversionContext): string {
    if (block.type === BlockType.GRID) {
      return this.convertGrid(block, context)
    } else if (block.type === BlockType.GRID_COLUMN) {
      return this.convertGridColumn(block, context)
    }
    return ''
  }

  private convertGrid(block: Blocks, context: ConversionContext): string {
    // 收集所有子列
    const columns: string[] = []

    if ('children' in block && block.children) {
      for (const child of block.children) {
        if (child.type === BlockType.GRID_COLUMN) {
          const columnHtml = this.convertGridColumn(child, context)
          if (columnHtml) {
            columns.push(columnHtml)
          }
        }
      }
    }

    if (columns.length === 0) {
      return ''
    }

    const className = this.getClassName('grid', context)
    const customStyle = this.getCustomStyle('grid', context)

    if (context.options.useInlineStyles !== false) {
      const defaultStyle = 'display: flex; flex-wrap: wrap; gap: 16px; margin: 16px 0; align-items: flex-start;'
      const style = customStyle ? ` style="${customStyle}"` : ` style="${defaultStyle}"`

      return `<div class="grid-container"${style}>${columns.join('')}</div>`
    } else {
      return `<div class="${className}">${columns.join('')}</div>`
    }
  }

  private convertGridColumn(block: Blocks, context: ConversionContext): string {
    // 使用基类提供的通用子块处理方法
    const childrenHtml = this.convertChildBlocks(block.children || [], context)

    if (childrenHtml.length === 0) {
      return ''
    }

    const className = this.getClassName('grid-column', context)
    const customStyle = this.getCustomStyle('grid-column', context)

    if (context.options.useInlineStyles !== false) {
      const defaultStyle = 'flex: 1; min-width: 0; box-sizing: border-box;'
      const style = customStyle ? ` style="${customStyle}"` : ` style="${defaultStyle}"`

      return `<div class="grid-column"${style}>${childrenHtml.join('')}</div>`
    } else {
      return `<div class="${className}">${childrenHtml.join('')}</div>`
    }
  }

  /**
   * 异步处理图片
   */
  private processImageAsync(block: Blocks, token: string): void {
    // 异步获取图片源并更新缓存
    this.fetchImageSources(block).then(sources => {
      if (sources) {
        // 优先使用originSrc，如果不存在则使用src
        const imageUrl = sources.originSrc || sources.src
        if (imageUrl) {
          // 获取图片数据并转换为dataURL
          fetch(imageUrl)
            .then(response => response.blob())
            .then(blob => this.blobToDataUrl(blob))
            .then(dataUrl => {
              imageCache.set(token, dataUrl)
              console.log(`✅ 图片缓存已更新: ${token}`)
            })
            .catch(error => {
              console.error(`❌ 图片处理失败 (${token}):`, error)
            })
        }
      }
    }).catch(error => {
      console.error(`❌ 获取图片源失败 (${token}):`, error)
    })
  }

  /**
   * 获取图片源
   */
  private async fetchImageSources(block: Blocks): Promise<{ src: string; originSrc: string } | null> {
    if (block.type !== BlockType.IMAGE) return null

    try {
      const imageManager = block.imageManager
      if (!imageManager?.fetch) return null

      const imageData = block.snapshot?.image
      if (!imageData?.token) return null

      // 使用imageManager获取图片源
      const sources = await imageManager.fetch(
        { token: imageData.token, isHD: false },
        {},
        (sources: { originSrc: string; src: string }) => sources
      )

      return sources
    } catch (error) {
      console.error('获取图片源失败:', error)
      return null
    }
  }

  /**
   * 异步获取图片数据
   */
  private async fetchImageData(imageUrl: string): Promise<Blob | null> {
    try {
      const response = await fetch(imageUrl)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return await response.blob()
    } catch (error) {
      console.error('获取图片数据失败:', error)
      return null
    }
  }

  /**
   * 将Blob转换为dataURL
   */
  private blobToDataUrl(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })
  }
} 