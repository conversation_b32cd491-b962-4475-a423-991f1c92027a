# 函数式HTML转换器

这是一个全新的函数式实现，用于替代原有的基于类的HTML转换器。主要解决了以下问题：

## 解决的问题

1. **样式重复问题**：原有实现中内联样式和CSS类名两套系统并存，导致样式定义重复
2. **复杂的类继承结构**：原有的类继承结构复杂，难以维护和扩展
3. **样式管理混乱**：样式分散在各个转换器中，难以统一管理

## 新架构特点

### 1. 函数式设计
- 使用纯函数替代类继承
- 每个转换器都是独立的函数
- 更容易测试和维护

### 2. 统一样式管理
- 所有样式定义在一个地方 (`styles.ts`)
- 内联样式和CSS类名使用相同的样式定义
- 样式只需要写一次

### 3. 清晰的模块划分
```
html-converter-fn/
├── index.ts          # 主入口
├── context.ts         # 上下文和类型定义
├── transform.ts       # 核心转换逻辑
├── styles.ts          # 统一样式管理
├── operations.ts      # 文本操作处理
├── image-processor.ts # 图片预处理
└── transformers/      # 各种块转换器
    ├── text.ts
    ├── heading.ts
    ├── image.ts
    └── ...
```

## 使用方法

### 基本用法

```typescript
import { convertDocxToHtml } from 'pkg/lark/html-converter-fn'

// 转换为HTML（使用内联样式）
const result = convertDocxToHtml(docx.rootBlock, {
  useInlineStyles: true,
  cssClassPrefix: 'my-prefix'
})

console.log(result.html)
```

### 使用CSS类名

```typescript
const result = convertDocxToHtml(docx.rootBlock, {
  useInlineStyles: false,
  cssClassPrefix: 'my-prefix'
})

console.log(result.html)
console.log(result.styles) // CSS样式表
```

### 预处理图片

```typescript
import { convertDocxToHtmlWithImages } from 'pkg/lark/html-converter-fn'

const result = await convertDocxToHtmlWithImages(docx.rootBlock, {
  useInlineStyles: true
})

console.log(result.html) // 图片已转换为data URL
```

## 样式系统

### 统一的样式定义
所有样式都在 `styles.ts` 中定义，包括：
- 默认样式配置
- 内联样式映射
- CSS类名生成

### 样式应用逻辑
1. 根据配置选择使用内联样式或CSS类名
2. 样式只在一个地方定义，避免重复
3. 支持自定义样式覆盖

## 扩展性

### 添加新的转换器
1. 在 `transformers/` 目录下创建新文件
2. 实现转换函数
3. 在 `transformers/index.ts` 中导出
4. 在 `transform.ts` 中注册

### 添加新样式
1. 在 `styles.ts` 的 `getInlineStyles()` 中添加样式定义
2. 在 `getDefaultStyles()` 中添加对应的CSS规则
3. 样式会自动应用到内联和CSS类名两种模式

## 与原有实现的兼容性

新的函数式实现提供了与原有实现相同的API：

```typescript
// 原有API
import { convertDocxToHtml } from 'pkg/lark/html-converter'

// 新API（相同的函数签名）
import { convertDocxToHtml } from 'pkg/lark/html-converter-fn'
```

可以通过简单的导入路径更改来切换到新实现。 