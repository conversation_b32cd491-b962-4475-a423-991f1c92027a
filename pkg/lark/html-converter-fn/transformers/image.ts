import { Blocks, BlockType } from '../../docx'
import { TransformContext, getStyleConfig, escapeHtml } from '../context'
import { getInlineStyles } from '../styles'
import { imageCache } from '../image-cache'

/**
 * 转换图片块
 */
export function transformImage(block: Blocks, context: TransformContext): string {
  if (block.type !== BlockType.IMAGE) return ''

  const imageData = (block.snapshot as any)?.image
  if (!imageData) return ''

  const { name, caption, width, height, token, scale } = imageData
  const align = (block.snapshot as any)?.align || 'left'
  const alt = extractAltText(caption) || name || '图片'

  // 检查缓存中是否已有这张图片
  const cachedSrc = imageCache.get(token)
  let src = cachedSrc || ''

  // 如果启用了图片转换且缓存中没有，则开始异步处理
  if (context.options.convertImages !== false && !cachedSrc) {
    // 创建图片元素，加入到上下文中
    const imgElement = document.createElement('img')
    imgElement.alt = alt
    imgElement.dataset.token = token
    imgElement.dataset.name = name

    // 异步处理图片，并更新缓存和DOM
    processImageAsync(block, token).catch(error => {
      console.error('图片处理失败:', error)
    })

    context.images.push(imgElement)
  }

  // 构建尺寸属性
  const sizeAttrs = buildSizeAttributes(width, height, scale)
  
  // 构建样式
  const inlineStyles = getInlineStyles()
  
  if (context.options.useInlineStyles) {
    // 使用内联样式
    const sizeStyle = buildSizeStyle(width, height, scale)
    const alignStyle = buildAlignStyle(align)
    const scaleStyle = buildScaleStyle(scale)
    const defaultStyle = 'max-width: 100%; height: auto; margin: 2px 0;'

    // 合并所有样式
    const combinedStyle = [defaultStyle, sizeStyle, alignStyle, scaleStyle].filter(Boolean).join(' ')
    const imgTag = `<img src="${src}" alt="${escapeHtml(alt)}" data-token="${escapeHtml(token)}" data-name="${escapeHtml(name)}" data-align="${escapeHtml(align)}" data-scale="${escapeHtml(scale?.toString() || '1')}" style="${combinedStyle}"${sizeAttrs}>`

    // 使用容器包装处理对齐
    const containerStyle = getContainerStyle(align)
    if (containerStyle) {
      return `<div style="${containerStyle}">${imgTag}</div>`
    }

    return imgTag
  } else {
    // 使用CSS类名
    const alignClass = getAlignClass(align)
    const imageStyleConfig = getStyleConfig('image', context, inlineStyles.image)
    const finalClassName = alignClass ? `${imageStyleConfig.className} ${alignClass}` : imageStyleConfig.className
    
    const imgTag = `<img src="${src}" alt="${escapeHtml(alt)}" data-token="${escapeHtml(token)}" data-name="${escapeHtml(name)}" data-align="${escapeHtml(align)}" data-scale="${escapeHtml(scale?.toString() || '1')}" class="${finalClassName}"${sizeAttrs}>`

    // 使用容器包装处理对齐
    const containerClass = getContainerClass(align)
    if (containerClass) {
      return `<div class="${containerClass}">${imgTag}</div>`
    }

    return imgTag
  }
}

/**
 * 异步处理图片
 */
async function processImageAsync(block: Blocks, token: string): Promise<void> {
  try {
    const dataUrl = await processImageSync(block)
    if (dataUrl) {
      // 更新缓存
      imageCache.set(token, dataUrl)

      // 更新DOM中所有相同token的img元素
      setTimeout(() => {
        const allImgsWithToken = document.querySelectorAll(`img[data-token="${token}"]`)
        allImgsWithToken.forEach(img => {
          if (img instanceof HTMLImageElement) {
            img.src = dataUrl
          }
        })
      }, 0)
    }
  } catch (error) {
    console.error('处理图片失败:', error)
  }
}

/**
 * 同步处理图片
 */
async function processImageSync(block: Blocks): Promise<string | null> {
  try {
    const imageData = (block.snapshot as any)?.image
    if (!imageData?.token) return null

    console.error('🔄 开始转换图片为dataURL:', imageData.name || 'unknown')

    // 获取图片源
    const sources = await fetchImageSources(block)
    console.error('📡 获取到图片源:', sources)

    if (!sources?.src) {
      console.error('❌ 无法获取图片源URL:', sources)
      return null
    }

    console.error('🌐 正在下载图片:', sources.src)

    // 获取图片数据
    const response = await fetch(sources.src)
    if (!response.ok) {
      console.error('❌ 图片下载失败:', response.status, response.statusText)
      return null
    }

    const blob = await response.blob()
    console.error('📦 图片下载成功，大小:', blob.size, 'bytes, 类型:', blob.type)

    // 转换为data URL
    const dataUrl = await blobToDataUrl(blob)

    console.error('✅ 图片转换为dataURL成功:', imageData.name || 'unknown', 'dataURL长度:', dataUrl.length)
    return dataUrl
  } catch (error) {
    console.error('❌ 处理图片失败:', error)
    return null
  }
}

/**
 * 获取图片源
 */
async function fetchImageSources(block: Blocks): Promise<{ src: string; originSrc: string } | null> {
  if (block.type !== BlockType.IMAGE) return null

  try {
    const imageManager = (block as any).imageManager
    if (!imageManager?.fetch) return null

    const imageData = (block.snapshot as any)?.image
    if (!imageData?.token) return null

    // 使用imageManager获取图片源
    const sources = await imageManager.fetch(
      { token: imageData.token, isHD: false },
      {},
      (sources: { originSrc: string; src: string }) => sources
    )

    return sources
  } catch (error) {
    console.error('获取图片源失败:', error)
    return null
  }
}

/**
 * 将Blob转换为data URL
 */
function blobToDataUrl(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = reject
    reader.readAsDataURL(blob)
  })
}

/**
 * 提取图片alt文本
 */
function extractAltText(caption?: any): string {
  if (!caption) return ''

  try {
    // 尝试从caption中提取文本
    const text = caption.text?.initialAttributedTexts?.text?.[0]
    return typeof text === 'string' ? text.trim() : ''
  } catch {
    return ''
  }
}

/**
 * 构建尺寸属性
 */
function buildSizeAttributes(width?: number, height?: number, scale?: number): string {
  const attrs: string[] = []
  const finalScale = scale || 1

  if (width && width > 0) {
    const scaledWidth = Math.round(width * finalScale)
    attrs.push(` width="${scaledWidth}"`)
  }

  if (height && height > 0) {
    const scaledHeight = Math.round(height * finalScale)
    attrs.push(` height="${scaledHeight}"`)
  }

  return attrs.join('')
}

/**
 * 构建尺寸样式
 */
function buildSizeStyle(width?: number, height?: number, scale?: number): string {
  const styles: string[] = []
  const finalScale = scale || 1

  if (width && width > 0) {
    const scaledWidth = Math.round(width * finalScale)
    styles.push(`width: ${scaledWidth}px`)
  }

  if (height && height > 0) {
    const scaledHeight = Math.round(height * finalScale)
    styles.push(`height: ${scaledHeight}px`)
  }

  return styles.join('; ')
}

/**
 * 构建缩放样式
 */
function buildScaleStyle(scale?: number): string {
  return ''
  // 如果scale不存在或为1，则不需要额外的transform样式
  if (!scale || scale === 1) {
    return ''
  }

  // 使用CSS transform来实现缩放
  return `transform: scale(${scale});`
}

/**
 * 构建对齐样式
 */
function buildAlignStyle(align: string): string {
  switch (align) {
    case 'left':
      return 'display: block !important;'
    case 'right':
      return 'display: block !important;'
    case 'center':
      return 'display: block !important; margin-left: auto !important; margin-right: auto !important;'
    default:
      return ''
  }
}

/**
 * 获取对齐CSS类名
 */
function getAlignClass(align: string): string {
  switch (align) {
    case 'left':
      return 'align-left'
    case 'right':
      return 'align-right'
    case 'center':
      return 'align-center'
    default:
      return ''
  }
}

/**
 * 获取容器样式
 */
function getContainerStyle(align: string): string {
  switch (align) {
    case 'left':
      return 'margin: 2px 0; text-align: left;'
    case 'right':
      return 'margin: 2px 0; text-align: right;'
    case 'center':
      return 'margin: 2px 0; text-align: center;'
    default:
      return ''
  }
}

/**
 * 获取容器CSS类名
 */
function getContainerClass(align: string): string {
  switch (align) {
    case 'left':
      return 'image-align-left'
    case 'right':
      return 'image-align-right'
    case 'center':
      return 'image-align-center'
    default:
      return ''
  }
} 